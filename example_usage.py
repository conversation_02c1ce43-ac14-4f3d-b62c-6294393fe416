from langchain_core.messages import HumanMessage
from agent.graph import graph

async def chat_example():
    """Example of using the chatbot with session and memory management."""
    
    # User session configuration
    session_id = "user_123_session_456"
    config = {
        "configurable": {
            "thread_id": session_id,  # Use session_id for thread isolation
            "model_name": "anthropic/claude-3.5-sonnet",
            "system_prompt": "You are a helpful customer service assistant."
        }
    }
    
    # First interaction
    inputs1 = {
        "messages": [HumanMessage(content="Hi, I'm looking for a restaurant")],
        "session_id": session_id,
        "user_id": "user_123",
        "latitude": "45.4666",
        "longitude": "9.1832",
        "memory_lenght": "15"  # Keep 15 messages in memory
    }
    
    result1 = await graph.ainvoke(inputs1, config)
    print("Bot:", result1["messages"][-1].content)
    
    # Follow-up interaction (remembers context)
    inputs2 = {
        "messages": [HumanMessage(content="Something Italian would be great")],
        "session_id": session_id,
        "memory_lenght": "15"
    }
    
    result2 = await graph.ainvoke(inputs2, config)
    print("Bot:", result2["messages"][-1].content)
    
    # Change memory limit mid-conversation
    inputs3 = {
        "messages": [HumanMessage(content="What did I ask for initially?")],
        "session_id": session_id,
        "memory_lenght": "5"  # Reduce to 5 messages
    }
    
    result3 = await graph.ainvoke(inputs3, config)
    print("Bot:", result3["messages"][-1].content)