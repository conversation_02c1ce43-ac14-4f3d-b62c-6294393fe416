"""Tools node for executing MCP tools in the CatchUp customer service system."""

from __future__ import annotations

from typing import Any, Dict, List
from langchain_core.messages import ToolMessage, AIMessage
from langchain_core.runnables import RunnableConfig
from langgraph.prebuilt import ToolNode

from agent.state import State
from shared.mcp_tools import get_catchup_tools


class CustomToolNode:
    """Custom tool node that handles invalid tool calls gracefully."""

    def __init__(self, tools: List):
        self.tools = tools
        self.tools_by_name = {tool.name: tool for tool in tools}
        self.base_tool_node = ToolNode(tools)

    async def ainvoke(self, state: State, config: RunnableConfig) -> Dict[str, Any]:
        """Execute tools with better error handling for invalid tool calls."""
        messages = state.get("messages", [])
        if not messages:
            return {"messages": []}

        last_message = messages[-1]
        if not isinstance(last_message, AIMessage):
            return {"messages": []}

        tool_messages = []

        # Handle valid tool calls
        if last_message.tool_calls:
            try:
                result = await self.base_tool_node.ainvoke(state, config)
                return result
            except Exception as e:
                print(f"Error executing valid tool calls: {e}")
                # Create error messages for each tool call
                for tool_call in last_message.tool_calls:
                    error_msg = ToolMessage(
                        content=f"Error executing tool {tool_call['name']}: {str(e)}",
                        tool_call_id=tool_call['id']
                    )
                    tool_messages.append(error_msg)

        # Handle invalid tool calls
        if hasattr(last_message, 'invalid_tool_calls') and last_message.invalid_tool_calls:
            for invalid_call in last_message.invalid_tool_calls:
                tool_name = invalid_call.get('name', 'unknown')
                tool_id = invalid_call.get('id', 'unknown')
                error = invalid_call.get('error', 'Unknown error')

                print(f"Handling invalid tool call: {tool_name} - {error}")

                # Special handling for get_categories with empty args
                if tool_name == 'get_categories' and 'not valid JSON' in error:
                    try:
                        # Try to execute the tool with empty dict
                        tool = self.tools_by_name.get('get_categories')
                        if tool:
                            result = await tool.ainvoke({})
                            tool_messages.append(ToolMessage(
                                content=str(result),
                                tool_call_id=tool_id
                            ))
                            continue
                    except Exception as e:
                        print(f"Failed to execute get_categories with empty args: {e}")

                # Create error message for invalid tool call
                error_msg = ToolMessage(
                    content=f"Invalid tool call for {tool_name}: {error}. Please check the tool arguments and try again.",
                    tool_call_id=tool_id
                )
                tool_messages.append(error_msg)

        return {"messages": tool_messages}


async def create_tools_node() -> CustomToolNode:
    """Create a custom tools node with CatchUp MCP tools.

    Returns:
        CustomToolNode configured with CatchUp MCP tools
    """
    tools = await get_catchup_tools()
    return CustomToolNode(tools)


async def execute_tools(state: State, config: RunnableConfig) -> Dict[str, Any]:
    """Execute tools based on tool calls in the last message.
    
    Args:
        state: Current conversation state
        config: Runtime configuration
        
    Returns:
        Updated state with tool execution results
    """
    # Get the tools
    tools = await get_catchup_tools()
    
    # Create tools node
    tools_node = ToolNode(tools)
    
    # Execute the tools node
    result = await tools_node.ainvoke(state, config)
    
    return result
