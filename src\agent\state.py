
from langgraph.graph.message import add_messages, AnyMessage
from typing import Optional, Annotated, Required, TypedDict, Any
from langchain_core.messages import BaseMessage


def memory_aware_reducer(existing: list[AnyMessage], new: list[AnyMessage]) -> list[AnyMessage]:
    """High-performance reducer that maintains memory limit during updates.

    Note: This reducer uses a default memory limit of 15 messages.
    For dynamic memory limits, use the limit_message_history function in call_model.py
    """
    # Fast path: if no existing messages, just return new ones
    if not existing:
        return new

    # Fast path: if no new messages, return existing
    if not new:
        return existing

    # Use default memory length of 15 (can be overridden in call_model)
    memory_length = 15

    # Early exit if memory_length is 0
    if memory_length <= 0:
        return []

    # Combine messages efficiently
    combined = existing + new

    # Only slice if we exceed the limit (avoid unnecessary operations)
    if len(combined) <= memory_length:
        return combined

    # Return only the most recent messages
    return combined[-memory_length:]


class State(TypedDict):
    """Chatbot state with message history and user context."""
    messages: Annotated[list[AnyMessage], memory_aware_reducer]
    session_id: Required[str] = None
    user_id: Required[str] = None
    latitude: Optional[str] = None
    longitude: Optional[str] = None
    memory_lenght: Optional[str] = 15
    email_address: Optional[str] = None
    isChat: Optional[bool] = True

   
