# LangGraph Server with Auth - Environment Configuration
# Copy this file to .env and fill in your values


# =============================================================================
# OPENAI API KEY (https://platform.openai.com/account/api-keys)
# =============================================================================

# OpenAI API key (required for our LangGraph agents)
OPENAI_API_KEY=

# =============================================================================
# LANGSMITH INTEGRATION (https://docs.smith.langchain.com/)
# =============================================================================

# LangSmith API key for tracing (required for deploying the langgraph server api)
LANGSMITH_API_KEY=

# Enable LangSmith tracing: true or false
LANGSMITH_TRACING=true

# LangSmith API endpoint (optional, change if using self-hosted LangSmith)
LANGSMITH_ENDPOINT="https://api.smith.langchain.com"

# LangSmith project name
LANGSMITH_PROJECT="rocket"

# =============================================================================
# SUPABASE
# =============================================================================

# Supabase database password
SUPABASE_PASSWORD=

# Database connection string
# This is the connection string for the postgres database where we store video and comment data
# Use the transaction connection string from the Supabase dashboard
SUPABASE_URI=

# =============================================================================
# YOUTUBE MCP
# =============================================================================

# YouTube API key (required for the YouTube MCP)
# https://console.cloud.google.com/
YOUTUBE_DATA_API_KEY=

# =============================================================================
# DATABASE
# =============================================================================

# Database connection string
# This is the connection string for the database used by the LangGraph server
# This will be a Postgres database on Render
DATABASE_URI=

# =============================================================================
# REDIS
# =============================================================================

# Redis connection string
# This is the connection string for the Redis used by the LangGraph server
# This will be a key-value store on Render
REDIS_URI=

# =============================================================================
# LANGGRAPH SERVER URL
# =============================================================================

# URL for the LangGraph server
# This is required for our frontend client to connect to the server
# For local development, use http://localhost:8000
# For Render, use the URL of your Render service (e.g., https://your-render-service.onrender.com)
LANGGRAPH_SERVER_URL="http://localhost:8000"

# =============================================================================
# AUTHENTICATION
# =============================================================================

# API key for authentication (leave empty to disable auth)
# Generate with: python scripts/api_key_generator.py
ROCKET_API_KEY=

# =============================================================================
# CORS CONFIGURATION
# =============================================================================

# Comma-separated list of allowed origins for CORS
# Examples:
# - For development: http://localhost:3000,http://127.0.0.1:3000
# - For production: https://yourdomain.com,https://www.yourdomain.com
# - Leave empty to disable CORS
CORS_ALLOWED_ORIGINS=

# =============================================================================
# SERVER CONFIGURATION
# =============================================================================

# Port for the proxy server (external facing)
PORT=8000

# Internal port for LangGraph server (should be different from PORT)
LANGGRAPH_INTERNAL_PORT=8123

# Environment: development, staging, or production
ENVIRONMENT=development

# Logging level: DEBUG, INFO, WARNING, ERROR
LOG_LEVEL=INFO
