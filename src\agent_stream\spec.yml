# This YAML was auto-generated based on an architecture 
# designed in LangGraph Builder (https://build.langchain.com).
#
# The YAML was used by langgraph-gen (https://github.com/langchain-ai/langgraph-gen-py) 
# to generate a code stub for a LangGraph application that follows the architecture.
#
# langgraph-gen is an open source CLI tool that converts YAML specifications into LangGraph code stubs.
#
# The code stub generated from this YAML can be found in stub.py.
#
# A placeholder implementation for the generated stub can be found in implementation.py.

name: CustomAgent
nodes:
  - name: Step 1
  - name: Step 2
  - name: Step 3
  - name: Node Left
  - name: Node 6
  - name: Node Right
edges:
  - from: __start__
    to: Step 1
  - from: Node 6
    to: __end__
  - from: Step 1
    to: Step 2
  - from: Step 2
    to: Step 3
  - from: Node Left
    to: Node 6
  - from: Node Right
    to: Node 6
  - from: Step 3
    condition: conditional_edge_1
    paths: [Node Left, Node Right]