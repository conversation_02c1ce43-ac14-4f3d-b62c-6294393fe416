"""This file was generated using `langgraph-gen` version 0.0.3.

This file provides a placeholder implementation for the corresponding stub.

Replace the placeholder implementation with your own logic.
"""

from typing_extensions import TypedDict, Annotated
import random
import time
import asyncio
from datetime import datetime

from agent_stream.stub import CustomAgent
from langgraph.types import Send
from langgraph.store.base import BaseStore
from langgraph.config import get_stream_writer
from langgraph.graph.message import add_messages, AnyMessage

class SomeState(TypedDict):
    messages: Annotated[list[AnyMessage], add_messages]
    # define your attributes here
    foo: str

wait = 4

def push_ui_message(component: str, props: dict, id: str):
    """Helper function to push UI messages using get_stream_writer"""
    writer = get_stream_writer()
    writer({
        "type": "ui",
        "component": component,
        "props": props,
        "id": id
    })
# Define stand-alone functions
async def Step_1(state: SomeState) -> dict:
    # push_ui_message(
    #     "SimpleStatus",
    #     {
    #         "text": "🔄 Step 1 starting...",
    #         "timestamp": datetime.now().isoformat()
    #     },
    #     id="step-1-status"
    # )
    await asyncio.sleep(wait)
    # push_ui_message(
    #     "SimpleStatus",
    #     {
    #         "text": "✅ Step 1 completed",
    #         "timestamp": datetime.now().isoformat()
    #     },
    #     id="step-1-status"
    # )
    return {}

async def Step_2(state: SomeState) -> dict:
    # push_ui_message(
    #     "SimpleStatus",
    #     {
    #         "text": "🔄 Step 2 starting...",
    #         "timestamp": datetime.now().isoformat()
    #     },
    #     id="step-2-status"
    # )
    await asyncio.sleep(wait)
    # push_ui_message(
    #     "SimpleStatus",
    #     {
    #         "text": "✅ Step 2 completed",
    #         "timestamp": datetime.now().isoformat()
    #     },
    #     id="step-2-status"
    # )
    return {}

async def Step_3(state: SomeState) -> dict:
    # push_ui_message(
    #     "SimpleStatus",
    #     {
    #         "text": "🔄 Step 3 starting...",
    #         "timestamp": datetime.now().isoformat()
    #     },
    #     id="step-3-status"
    # )
    await asyncio.sleep(wait)
    # push_ui_message(
    #     "SimpleStatus",
    #     {
    #         "text": "✅ Step 3 completed",
    #         "timestamp": datetime.now().isoformat()
    #     },
    #     id="step-3-status"
    # )
    return { state["messages"]: [Send(content="Step 3 completed")] }

async def Node_Left(state: SomeState) -> dict:
    # push_ui_message(
    #     "SimpleStatus",
    #     {
    #         "text": "🔄 Node Left starting...",
    #         "timestamp": datetime.now().isoformat()
    #     },
    #     id="node-left-status"
    # )
    await asyncio.sleep(wait)
    # push_ui_message(
    #     "SimpleStatus",
    #     {
    #         "text": "✅ Node Left completed",
    #         "timestamp": datetime.now().isoformat()
    #     },
    #     id="node-left-status"
    # )
    return { state["messages"]: [Send(content="Node Left completed")] }

async def Node_Right(state: SomeState) -> dict:
    # push_ui_message(
    #     "SimpleStatus",
    #     {
    #         "text": "🔄 Node Right starting...",
    #         "timestamp": datetime.now().isoformat()
    #     },
    #     id="node-right-status"
    # )
    await asyncio.sleep(wait)
    # push_ui_message(
    #     "SimpleStatus",
    #     {
    #         "text": "✅ Node Right completed",
    #         "timestamp": datetime.now().isoformat()
    #     },
    #     id="node-right-status"
    # )
    return { state["messages"]: [Send(content="Node Right completed")] }

async def Node_6(state: SomeState) -> dict:
    # push_ui_message(
    #     "SimpleStatus",
    #     {
    #         "text": "🔄 Node 6 starting...",
    #         "timestamp": datetime.now().isoformat()
    #     },
    #     id="node-6-status"
    # )
    await asyncio.sleep(wait)
    # push_ui_message(
    #     "SimpleStatus",
    #     {
    #         "text": "✅ Node 6 completed",
    #         "timestamp": datetime.now().isoformat()
    #     },
    #     id="node-6-status"
    # )
    return { state["messages"]: [Send(content="Node 6 completed")] }


def conditional_edge_1(state: SomeState) -> str:
    print("In condition: conditional_edge_1")
    # Randomly choose between the two possible paths
    return random.choice(["Node Left", "Node Right"])


agent = CustomAgent(
    state_schema=SomeState,
    impl=[
        ("Step_1", Step_1),
        ("Step_2", Step_2),
        ("Step_3", Step_3),
        ("Node_Left", Node_Left),
        ("Node_6", Node_6),
        ("Node_Right", Node_Right),
        ("conditional_edge_1", conditional_edge_1),
    ],
)

compiled_agent = agent.compile()


