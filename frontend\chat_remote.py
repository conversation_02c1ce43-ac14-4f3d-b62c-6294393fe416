from api.agent_server import create_thread, search_threads, delete_thread, run_stream_from_message, SSEParser
from uuid import UUI<PERSON>
from colorama import Fore, Style
import random
import nest_asyncio
nest_asyncio.apply()


async def main():
    user_id = UUID("00000000-0000-0000-0000-000000000000")
    
    
    memory_session_id = str(random.choice(range(100))) + "-" + str(random.choice(range(100))) + "-" + str(random.choice(range(100))) + "-" + str(random.choice(range(100)))
    print
    try:
        thread_id = await create_thread(user_id)
        print(f"\nCreated thread: {thread_id}")

        threads = await search_threads(user_id)
        print(f"\nFound threads: {threads}")

        configurable = {
            "thread_id": str(thread_id)
        }

    

        # Create a persistent parser to track seen messages across the conversation
        parser = SSEParser()

        user_input = "Briefly introduce yourself and offer to help me. Use italian language."
        while True:
            print(f"\n ---- 🚀 CatchUp ---- \n")
            async for result in run_stream_from_message(
                thread_id=thread_id,
                assistant_id="catchup",
                message=user_input,
                configurable=configurable,
                parser=parser,
                user_id="fe95e629-0a4e-474b-97d1-fafe9d6863e3",
                email_address="<EMAIL>",
                latitude="45.4666",
                longitude="9.1832",
                session_id= memory_session_id,
                memory_lenght="15"
                ):
                print(Fore.CYAN + result + Style.RESET_ALL, end="", flush=True)

            user_input = input("\n\nUser ('exit' to quit): ")
            if user_input.lower() in ["exit", "quit"]:
                print("\n\nExit command received. Exiting...\n\n")
                break
            print(f"\n\n ----- 🥷 Utente ----- \n\n{user_input}\n")

        # Clean up
        await delete_thread(thread_id)
        print(f"\nDeleted thread: {thread_id}")
    except Exception as e:
        print(f"Error: {type(e).__name__}: {str(e)}")
        raise
    
if __name__ == "__main__":
    import asyncio

    asyncio.run(main())
