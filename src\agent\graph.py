"""LangGraph chatbot graph with checkpointing.

A simple chatbot that uses OpenRouter LLM via llm_factory with conversation persistence.
"""

from __future__ import annotations

import sys
from pathlib import Path

# Add the src directory to Python path
current_dir = Path(__file__).parent
src_dir = current_dir.parent
if str(src_dir) not in sys.path:
    sys.path.insert(0, str(src_dir))

from typing import TypedDict, Any, Dict
from langchain_core.messages import AIMessage
from langchain_core.runnables import RunnableConfig
from langgraph.graph import StateGraph
from langgraph.checkpoint.memory import MemorySaver

from agent.nodes import call_model
from agent.nodes.tools_node import create_tools_node
from agent.state import State


class Configuration(TypedDict):
    """Configurable parameters for the chatbot."""
    model_name: str
    system_prompt: str


def should_route_to_tools(state: State) -> str:
    """Determine whether to route to tools or end the conversation.

    Args:
        state: The current state containing messages

    Returns:
        "tools" if the last AI message has tool calls, "__end__" otherwise
    """
    messages = state.get("messages", [])

    if not messages:
        return "__end__"

    last_message = messages[-1]

    # Check if the last message is an AI message with tool calls (valid or invalid)
    if isinstance(last_message, AIMessage):
        # Check for valid tool calls
        if last_message.tool_calls:
            print(f"Found {len(last_message.tool_calls)} valid tool calls")
            return "tools"

        # Check for invalid tool calls - these also need to be processed
        if hasattr(last_message, 'invalid_tool_calls') and last_message.invalid_tool_calls:
            print(f"Found {len(last_message.invalid_tool_calls)} invalid tool calls")
            # Route to tools so they can handle the error and provide feedback
            return "tools"

    return "__end__"


# Global tools node instance
_tools_node = None


async def tools_node(state: State, config: RunnableConfig) -> Dict[str, Any]:
    """Tools node wrapper that handles tool execution."""
    global _tools_node

    # Create tools node if not already created
    if _tools_node is None:
        _tools_node = await create_tools_node()

    # Execute the tools node
    return await _tools_node.ainvoke(state, config)


# Create checkpointer for conversation persistence
#checkpointer = MemorySaver()

# Define the graph with checkpointing
graph = (
    StateGraph(State, config_schema=Configuration)
    .add_node("call_model", call_model)
    .add_node("tools", tools_node)
    .add_edge("__start__", "call_model")
    .add_conditional_edges("call_model", should_route_to_tools,["tools", "__end__"])
    .add_edge("tools", "call_model")
    .compile()
)


