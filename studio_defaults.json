{"input_schema": {"type": "object", "properties": {"messages": {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "string", "enum": ["human", "ai", "system"]}, "content": {"type": "string"}}, "required": ["type", "content"]}, "description": "Array of messages in the conversation"}, "user_id": {"type": "string", "description": "Unique identifier for the user"}, "email_address": {"type": "string", "format": "email", "description": "User's email address"}, "latitude": {"type": "string", "description": "User's latitude position"}, "longitude": {"type": "string", "description": "User's longitude position"}, "session_id": {"type": "string", "description": "Memory session identifier"}, "memory_lenght": {"type": "string", "description": "Memory length parameter"}}, "required": ["messages"]}, "default_input": {"messages": [{"type": "human", "content": "Cerco per i prossimi giorni, un aperitivo"}], "user_id": "fe95e629-0a4e-474b-97d1-fafe9d6863e3", "email_address": "<EMAIL>", "latitude": "45.4666", "longitude": "9.1832", "session_id": "1233", "memory_lenght": "15"}, "config_schema": {"type": "object", "properties": {"model_name": {"type": "string", "default": "anthropic/claude-3.5-sonnet", "description": "LLM model to use"}, "system_prompt": {"type": "string", "default": "You are a helpful customer service assistant for a multi-tenant marketplace.", "description": "System prompt for the AI assistant"}}}, "default_config": {"configurable": {"model_name": "anthropic/claude-3.5-sonnet", "system_prompt": "You are a helpful customer service assistant for a multi-tenant marketplace."}}}