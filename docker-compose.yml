services:
  langgraph-server:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: catchup-server
    ports:
      - "8000:8000"  # Map container port 8000 to host port 8000
    env_file:
      - .env  # Load environment variables from .env file
    environment:
      # Override any specific environment variables if needed
      - PYTHONPATH=/deps/LanCatchUp
      - LOG_LEVEL=DEBUG  # Add this for more verbose logging
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/ok"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - langgraph-network

networks:
  langgraph-network:
    driver: bridge
