"""MCP tools integration for CatchUp customer service system."""

from __future__ import annotations

import asyncio
from typing import List, Optional
from langchain_core.tools import BaseTool
from langchain_mcp_adapters.client import MultiServerMCPClient
from langchain_mcp_adapters.tools import load_mcp_tools
from mcp import ClientSession
from mcp.client.sse import sse_client


class MCPToolsManager:
    """Manager for MCP tools integration."""
    
    def __init__(self, server_url: str):
        """Initialize MCP tools manager.
        
        Args:
            server_url: The URL of the MCP server (SSE endpoint)
        """
        self.server_url = server_url
        self._tools: Optional[List[BaseTool]] = None
        self._client: Optional[MultiServerMCPClient] = None
    
    async def initialize(self) -> None:
        """Initialize the MCP client and load tools."""
        try:
            # Create MultiServerMCPClient for SSE transport
            self._client = MultiServerMCPClient({
                "catchup": {
                    "transport": "sse",
                    "url": self.server_url,
                }
            })
            
            # Load tools from the MCP server
            self._tools = await self._client.get_tools()
            print(f"Successfully loaded {len(self._tools)} tools from CatchUpMCP server")
            
        except Exception as e:
            print(f"Failed to initialize MCP tools: {e}")
            self._tools = []
    
    async def get_tools(self) -> List[BaseTool]:
        """Get the loaded MCP tools.
        
        Returns:
            List of LangChain tools loaded from the MCP server
        """
        if self._tools is None:
            await self.initialize()
        
        return self._tools or []
    
    async def close(self) -> None:
        """Close the MCP client connection."""
        if self._client:
            # The MultiServerMCPClient doesn't have an explicit close method
            # but we can set it to None to allow garbage collection
            self._client = None
        self._tools = None


# Global instance for the MCP tools manager
_mcp_manager: Optional[MCPToolsManager] = None


async def get_mcp_tools_manager() -> MCPToolsManager:
    """Get or create the global MCP tools manager."""
    global _mcp_manager
    
    if _mcp_manager is None:
        _mcp_manager = MCPToolsManager("https://genenrativepangea.app.n8n.cloud/mcp/catchup/sse")
        await _mcp_manager.initialize()
    
    return _mcp_manager


async def get_catchup_tools() -> List[BaseTool]:
    """Get the CatchUp MCP tools.
    
    Returns:
        List of LangChain tools for the CatchUp customer service system
    """
    manager = await get_mcp_tools_manager()
    return await manager.get_tools()
