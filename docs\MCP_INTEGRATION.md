# MCP Integration for CatchUp Customer Service

This document explains the Model Context Protocol (MCP) integration in the CatchUp customer service system.

## Overview

The CatchUp system integrates with an MCP server that provides customer service tools for:
- Category management
- Deal searching and management
- User details retrieval
- Booking management
- Communication tools (email, WhatsApp)

## MCP Server Details

- **Server URL**: `https://genenrativepangea.app.n8n.cloud/mcp/catchup/sse`
- **Transport**: Server-Sent Events (SSE)
- **Tools Available**: 10+ customer service tools

## Architecture

### Components

1. **MCPToolsManager** (`src/shared/mcp_tools.py`)
   - Manages connection to the MCP server
   - Loads and caches tools from the server
   - Handles connection lifecycle

2. **Tools Node** (`src/agent/nodes/tools_node.py`)
   - Executes MCP tools when called by the LLM
   - Integrates with LangGraph's tool execution framework

3. **Enhanced Call Model** (`src/agent/nodes/call_model.py`)
   - Binds MCP tools to the LLM
   - Maintains existing system prompt functionality

4. **Updated Graph** (`src/agent/graph.py`)
   - Includes conditional edges for tool calling
   - Routes between model calls and tool execution

### Flow

1. User sends a message
2. `call_model` node processes the message with bound tools
3. If LLM decides to use tools, flow goes to `tools` node
4. Tools are executed and results returned
5. Flow returns to `call_model` for final response
6. Response is returned to user

## Available Tools

The MCP server provides the following tools (as described in the system prompt):

- `get_categories` - Read all categories
- `search_deals` - Search deals and offers for specific categories
- `get_user_details` - Read information about the user
- `get_chat_history` - Read chat history
- `get_deals` - Get deals and offers from business/company
- `get_business_details` - Read company information
- `get_booking_details` - Get details about existing bookings
- `create_booking` - Create a booking for a deal
- `sent_email_to_users` - Send HTML emails to users
- `whatsapp_sent_tool` - Send WhatsApp messages

## Configuration

### Dependencies

The integration requires the `langchain-mcp-adapters` package:

```toml
dependencies = [
    # ... other dependencies
    "langchain-mcp-adapters>=0.1.9",
]
```

### Environment Variables

No additional environment variables are required for the MCP integration itself, but the underlying LLM may require API keys (e.g., `OPENROUTER_API_KEY`).

## Testing

### Manual Testing

Run the test script to verify the integration:

```bash
python test_mcp_connection.py
```

This will:
1. Test connection to the MCP server
2. Load and display available tools
3. Test the complete graph with tool integration

### Integration Tests

Run the integration tests:

```bash
pytest tests/integration_tests/test_mcp_integration.py -v
```

Note: These tests are skipped by default as they require the MCP server to be available.

## Technical Challenges and Solutions

### Challenge 1: Invalid Tool Call Handling

**Problem**: The LLM was generating invalid tool calls for the `get_categories` tool, causing JSON parsing errors.

**Root Cause**:
- The `get_categories` tool expects an empty JSON object `{}` as arguments
- The LLM was calling it with empty string arguments `''` instead
- This caused a `JSONDecodeError: Expecting value: line 1 column 1 (char 0)`

**Error Example**:
```
invalid_tool_calls=[{
    'name': 'get_categories',
    'args': '',  # ← Should be {}
    'id': 'toolu_01AT5kEjRF7b6dRvySPWDvHr',
    'error': 'Function get_categories arguments:\n\n\n\nare not valid JSON...'
}]
```

**Solution**: Created a custom `CustomToolNode` class that:

1. **Graceful Error Handling**: Catches invalid tool calls and processes them appropriately
2. **Automatic Correction**: Specifically handles the `get_categories` empty args issue by calling it with `{}` instead of `''`
3. **Fallback Processing**: Uses the standard `ToolNode` for valid tool calls
4. **Meaningful Error Messages**: Provides clear feedback for other invalid tool calls

**Implementation** (`src/agent/nodes/tools_node.py`):
```python
class CustomToolNode:
    def __init__(self, tools: List):
        self.tools = tools
        self.tools_by_name = {tool.name: tool for tool in tools}
        self.base_tool_node = ToolNode(tools)

    async def ainvoke(self, state: State, config: RunnableConfig):
        # Handle valid tool calls with standard ToolNode
        if last_message.tool_calls:
            return await self.base_tool_node.ainvoke(state, config)

        # Handle invalid tool calls with custom logic
        if hasattr(last_message, 'invalid_tool_calls'):
            for invalid_call in last_message.invalid_tool_calls:
                if tool_name == 'get_categories' and 'not valid JSON' in error:
                    # Auto-correct: call with empty dict instead of empty string
                    tool = self.tools_by_name.get('get_categories')
                    result = await tool.ainvoke({})
                    # Return successful result
```

### Challenge 2: LangGraph Node Function Signature

**Problem**: Graph compilation failed with error: `create_tools_node() takes 0 positional arguments but 1 was given`

**Root Cause**:
- LangGraph expects node functions to accept `(state, config)` parameters
- `create_tools_node()` was an async factory function that returned a tools node instance
- The graph was trying to call it as a node function directly

**Solution**: Created a wrapper function that bridges the gap:

**Implementation** (`src/agent/graph.py`):
```python
# Global tools node instance for reuse
_tools_node = None

async def tools_node(state: State, config: RunnableConfig) -> Dict[str, Any]:
    """Tools node wrapper that handles tool execution."""
    global _tools_node

    # Lazy initialization - create tools node only once
    if _tools_node is None:
        _tools_node = await create_tools_node()

    # Execute the tools node with proper parameters
    return await _tools_node.ainvoke(state, config)

# Use wrapper in graph definition
graph = (
    StateGraph(State, config_schema=Configuration)
    .add_node("call_model", call_model)
    .add_node("tools", tools_node)  # ← Use wrapper function
    # ...
)
```

### Challenge 3: Conditional Edge Routing

**Problem**: The `conditional_edge_1` function was not defined, causing compilation errors.

**Solution**: Implemented proper routing logic that handles both valid and invalid tool calls:

```python
def conditional_edge_1(state: State) -> str:
    """Determine whether to route to tools or end the conversation."""
    messages = state.get("messages", [])
    if not messages:
        return "__end__"

    last_message = messages[-1]
    if isinstance(last_message, AIMessage):
        # Route to tools for valid tool calls
        if last_message.tool_calls:
            return "tools"

        # Route to tools for invalid tool calls (so they can be handled)
        if hasattr(last_message, 'invalid_tool_calls') and last_message.invalid_tool_calls:
            return "tools"

    return "__end__"
```

**Key Insight**: Invalid tool calls should still be routed to the tools node for proper error handling and correction, rather than ending the conversation.

### Results

After implementing these solutions:

✅ **Invalid tool calls are automatically corrected**
✅ **The `get_categories` tool works reliably**
✅ **Multi-step tool workflows function properly** (get_categories → search_deals → response)
✅ **Error handling is graceful and informative**
✅ **The system maintains conversation flow even with tool errors**

**Test Results**:
```
Input: "Cerco per i prossimi giorni, un aperitivo"
✅ get_categories called (auto-corrected from invalid args)
✅ search_deals called with "Launch Bar" category
✅ Found 2 aperitivo deals with proper pricing and availability
✅ Response includes required JSON structure with relatedIds
```

## Troubleshooting

### Common Issues

1. **No tools loaded**
   - Check if the MCP server is accessible
   - Verify the server URL is correct
   - Check network connectivity

2. **Tool execution errors**
   - Verify the tool parameters match the expected schema
   - Check if the MCP server is responding correctly

3. **Graph execution issues**
   - Ensure all dependencies are installed
   - Check that the LLM has the necessary API keys

4. **Invalid tool call errors**
   - Check the `CustomToolNode` implementation
   - Verify that tool schemas are properly defined
   - Look for JSON parsing errors in tool arguments

### Debug Mode

To enable debug logging, modify the `MCPToolsManager` to include more verbose logging:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Development

### Adding New Tools

If new tools are added to the MCP server, they will automatically be available in the system after restarting the application. No code changes are required.

### Modifying Tool Behavior

Tool behavior is controlled by the MCP server. To modify how tools work, update the server implementation, not the client code.

### Custom Tool Processing

If you need custom processing for specific tools, you can modify the `execute_tools` function in `src/agent/nodes/tools_node.py`.

## Security Considerations

- The MCP server should implement proper authentication and authorization
- Tool parameters should be validated on the server side
- Sensitive information should not be logged in tool calls
- Rate limiting should be implemented to prevent abuse

## Performance

- Tools are loaded once at startup and cached
- The MCP connection is reused across multiple tool calls
- Consider implementing connection pooling for high-traffic scenarios
