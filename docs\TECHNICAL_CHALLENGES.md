# Technical Challenges and Solutions

This document details the technical challenges encountered during the development of the CatchUp LangGraph customer service system and their solutions.

## Overview

During the integration of MCP tools with LangGraph, we encountered several critical issues that required custom solutions. These challenges highlight important considerations when working with LLM tool calling, async operations in LangGraph, and error handling in production systems.

## Challenge 1: Invalid Tool Call JSON Parsing

### Problem Description

**Severity**: Critical - System failure  
**Impact**: Complete breakdown of tool calling functionality  
**Frequency**: Consistent with `get_categories` tool

The LLM was generating invalid tool calls that caused JSON parsing errors, preventing the system from executing tools and continuing conversations.

### Root Cause Analysis

1. **Tool Schema Mismatch**: The `get_categories` tool expects no arguments (empty JSON object `{}`)
2. **LLM Behavior**: The LLM was calling the tool with empty string arguments (`''`) instead
3. **JSON Parser Failure**: <PERSON><PERSON><PERSON><PERSON>'s tool parser couldn't handle the empty string as valid JSON

### Error Manifestation

```python
# Error in AI message
invalid_tool_calls=[{
    'name': 'get_categories',
    'args': '',  # ← Problem: empty string instead of empty object
    'id': 'toolu_01AT5kEjRF7b6dRvySPWDvHr',
    'error': 'Function get_categories arguments:\n\n\n\nare not valid JSON. Received JSONDecodeError Expecting value: line 1 column 1 (char 0)',
    'type': 'invalid_tool_call'
}]
```

### Solution Implementation

Created a custom `CustomToolNode` class with intelligent error handling:

```python
class CustomToolNode:
    """Custom tool node that handles invalid tool calls gracefully."""
    
    def __init__(self, tools: List):
        self.tools = tools
        self.tools_by_name = {tool.name: tool for tool in tools}
        self.base_tool_node = ToolNode(tools)
    
    async def ainvoke(self, state: State, config: RunnableConfig):
        """Execute tools with better error handling for invalid tool calls."""
        last_message = messages[-1]
        
        # Handle valid tool calls normally
        if last_message.tool_calls:
            return await self.base_tool_node.ainvoke(state, config)
        
        # Handle invalid tool calls with custom logic
        if hasattr(last_message, 'invalid_tool_calls'):
            for invalid_call in last_message.invalid_tool_calls:
                tool_name = invalid_call.get('name')
                
                # Special handling for get_categories with empty args
                if tool_name == 'get_categories' and 'not valid JSON' in error:
                    tool = self.tools_by_name.get('get_categories')
                    result = await tool.ainvoke({})  # Call with empty dict
                    return {"messages": [ToolMessage(content=str(result), tool_call_id=tool_id)]}
```

### Key Benefits

- **Automatic Error Correction**: Fixes the most common tool calling error automatically
- **Graceful Degradation**: System continues to function even with invalid tool calls
- **Backward Compatibility**: Valid tool calls continue to work unchanged
- **Debugging Support**: Clear error messages for unhandled invalid calls

## Challenge 2: LangGraph Node Function Signature Mismatch

### Problem Description

**Severity**: Critical - Graph compilation failure  
**Impact**: System cannot start  
**Error**: `create_tools_node() takes 0 positional arguments but 1 was given`

### Root Cause Analysis

1. **LangGraph Expectation**: Node functions must accept `(state, config)` parameters
2. **Factory Function**: `create_tools_node()` was an async factory that returns a tools node
3. **Signature Mismatch**: Graph tried to call factory as a node function directly

### Solution Implementation

Created a wrapper function that bridges the gap between factory and node requirements:

```python
# Global tools node instance for reuse
_tools_node = None

async def tools_node(state: State, config: RunnableConfig) -> Dict[str, Any]:
    """Tools node wrapper that handles tool execution."""
    global _tools_node
    
    # Lazy initialization - create tools node only once
    if _tools_node is None:
        _tools_node = await create_tools_node()
    
    # Execute the tools node with proper parameters
    return await _tools_node.ainvoke(state, config)

# Graph definition
graph = (
    StateGraph(State, config_schema=Configuration)
    .add_node("call_model", call_model)
    .add_node("tools", tools_node)  # Use wrapper function
    .add_edge("__start__", "call_model")
    .add_conditional_edges("call_model", conditional_edge_1)
    .add_edge("tools", "call_model")
    .compile()
)
```

### Design Patterns Applied

- **Lazy Initialization**: Tools node created only when first needed
- **Singleton Pattern**: Single tools node instance reused across calls
- **Adapter Pattern**: Wrapper adapts factory function to node function interface

## Challenge 3: Missing Conditional Edge Function

### Problem Description

**Severity**: Critical - Graph compilation failure  
**Impact**: System cannot determine routing logic  
**Error**: `"conditional_edge_1" is not defined`

### Solution Implementation

Implemented comprehensive routing logic that handles both valid and invalid tool calls:

```python
def conditional_edge_1(state: State) -> str:
    """Determine whether to route to tools or end the conversation."""
    messages = state.get("messages", [])
    
    if not messages:
        return "__end__"
    
    last_message = messages[-1]
    
    if isinstance(last_message, AIMessage):
        # Route to tools for valid tool calls
        if last_message.tool_calls:
            return "tools"
        
        # Route to tools for invalid tool calls (for error handling)
        if hasattr(last_message, 'invalid_tool_calls') and last_message.invalid_tool_calls:
            return "tools"
    
    return "__end__"
```

### Key Design Decision

**Invalid tool calls are routed to tools node** rather than ending the conversation. This allows:
- Error correction and recovery
- Meaningful error messages to the LLM
- Continuation of conversation flow

## Lessons Learned

### 1. LLM Tool Calling Reliability

- **Issue**: LLMs may generate invalid tool calls even with clear schemas
- **Solution**: Always implement robust error handling for tool calls
- **Best Practice**: Create custom tool nodes for production systems

### 2. Async Function Integration in LangGraph

- **Issue**: Factory functions don't match LangGraph node signatures
- **Solution**: Use wrapper functions with lazy initialization
- **Best Practice**: Separate creation logic from execution logic

### 3. Error Handling Strategy

- **Issue**: Errors can break conversation flow
- **Solution**: Route errors through the system for handling rather than failing fast
- **Best Practice**: Design for graceful degradation

### 4. Testing Complex Integrations

- **Issue**: Integration issues only appear in full system tests
- **Solution**: Create comprehensive test scenarios that cover error cases
- **Best Practice**: Test both happy path and error scenarios

## Future Considerations

### 1. Tool Schema Validation

Consider implementing client-side tool schema validation to catch issues before they reach the LLM.

### 2. Error Monitoring

Implement monitoring for invalid tool calls to identify patterns and improve tool definitions.

### 3. LLM Fine-tuning

Consider fine-tuning the LLM on proper tool calling patterns for this specific domain.

### 4. Retry Logic

Implement intelligent retry logic for failed tool calls with corrected parameters.

## Testing Verification

The solutions were verified with comprehensive testing:

```bash
# Test input
"Cerco per i prossimi giorni, un aperitivo"

# Results
✅ Invalid get_categories call auto-corrected
✅ Categories retrieved successfully  
✅ search_deals executed with "Launch Bar" category
✅ Found 2 aperitivo deals with complete information
✅ Proper JSON response format maintained
✅ Conversation flow preserved
```

This demonstrates that the system now handles the complete workflow from invalid tool calls through to successful task completion.
