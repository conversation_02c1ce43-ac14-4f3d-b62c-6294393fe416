"""This is an automatically generated file. Do not modify it.

This file was generated using `langgraph-gen` version 0.0.3.
To regenerate this file, run `langgraph-gen` with the source `yaml` file as an argument.

Usage:

1. Add the generated file to your project.
2. Create a new agent using the stub.

Below is a sample implementation of the generated stub:

```python
from typing_extensions import TypedDict

from stub import CustomAgent

class SomeState(TypedDict):
    # define your attributes here
    foo: str

# Define stand-alone functions
def Step_1(state: SomeState) -> dict:
    print("In node: Step 1")
    return {
        # Add your state update logic here
    }


def Step_2(state: SomeState) -> dict:
    print("In node: Step 2")
    return {
        # Add your state update logic here
    }


def Step_3(state: SomeState) -> dict:
    print("In node: Step 3")
    return {
        # Add your state update logic here
    }


def Node_Left(state: SomeState) -> dict:
    print("In node: Node Left")
    return {
        # Add your state update logic here
    }


def Node_6(state: SomeState) -> dict:
    print("In node: Node 6")
    return {
        # Add your state update logic here
    }


def Node_Right(state: SomeState) -> dict:
    print("In node: Node Right")
    return {
        # Add your state update logic here
    }


def conditional_edge_1(state: SomeState) -> str:
    print("In condition: conditional_edge_1")
    raise NotImplementedError("Implement me.")


agent = CustomAgent(
    state_schema=SomeState,
    impl=[
        ("Step 1", Step_1),
        ("Step 2", Step_2),
        ("Step 3", Step_3),
        ("Node Left", Node_Left),
        ("Node 6", Node_6),
        ("Node Right", Node_Right),
        ("conditional_edge_1", conditional_edge_1),
    ]
)

compiled_agent = agent.compile()

print(compiled_agent.invoke({"foo": "bar"}))
"""

from typing import Callable, Any, Optional, Type

from langgraph.constants import START, END
from langgraph.graph import StateGraph


def CustomAgent(
    *,
    state_schema: Optional[Type[Any]] = None,
    config_schema: Optional[Type[Any]] = None,
    input: Optional[Type[Any]] = None,
    output: Optional[Type[Any]] = None,
    impl: list[tuple[str, Callable]],
) -> StateGraph:
    """Create the state graph for CustomAgent."""
    # Declare the state graph
    builder = StateGraph(
        state_schema, config_schema=config_schema, input=input, output=output
    )

    nodes_by_name = {name: imp for name, imp in impl}

    all_names = set(nodes_by_name)

    expected_implementations = {
        "Step_1",
        "Step_2",
        "Step_3",
        "Node_Left",
        "Node_6",
        "Node_Right",
        "conditional_edge_1",
    }

    missing_nodes = expected_implementations - all_names
    if missing_nodes:
        raise ValueError(f"Missing implementations for: {missing_nodes}")

    extra_nodes = all_names - expected_implementations

    if extra_nodes:
        raise ValueError(
            f"Extra implementations for: {extra_nodes}. Please regenerate the stub."
        )

    # Add nodes
    builder.add_node("Step 1", nodes_by_name["Step_1"])
    builder.add_node("Step 2", nodes_by_name["Step_2"])
    builder.add_node("Step 3", nodes_by_name["Step_3"])
    builder.add_node("Node Left", nodes_by_name["Node_Left"])
    builder.add_node("Node 6", nodes_by_name["Node_6"])
    builder.add_node("Node Right", nodes_by_name["Node_Right"])

    # Add edges
    builder.add_edge(START, "Step 1")
    builder.add_edge("Node 6", END)
    builder.add_edge("Step 1", "Step 2")
    builder.add_edge("Step 2", "Step 3")
    builder.add_edge("Node Left", "Node 6")
    builder.add_edge("Node Right", "Node 6")
    builder.add_conditional_edges(
        "Step 3",
        nodes_by_name["conditional_edge_1"],
        [
            "Node Left",
            "Node Right",
        ],
    )
    return builder
